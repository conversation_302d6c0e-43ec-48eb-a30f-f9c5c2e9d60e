import 'dotenv/config'
import 'express-async-errors'

import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'

import { errorHandler, handleUnhandledRejection, handleUncaughtException } from './middleware/errorHandler.js'
import { rateLimiter } from './middleware/rateLimiter.js'
import {
  generalLimiter,
  speedLimiter,
  sanitizeInput,
  requestSizeLimiter,
  securityHeaders,
  ipFilter,
  securityLogger,
  corsOptions,
  cspOptions
} from './middleware/security.js'
import logger from './utils/logger.js'

// Import routes
import authRoutes from './routes/auth.js'
import userRoutes from './routes/users.js'
import groupRoutes from './routes/groups.js'
import roleRoutes from './routes/roles.js'
import moduleRoutes from './routes/modules.js'
import permissionRoutes from './routes/permissions.js'
import accessControlRoutes from './routes/accessControl.js'

const app = express()
const PORT = process.env.PORT || 3001

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1)

// Security middleware (order matters!)
app.use(ipFilter) // IP filtering first
app.use(securityHeaders) // Custom security headers
app.use(helmet({ contentSecurityPolicy: cspOptions })) // Helmet with CSP
app.use(cors(corsOptions)) // Enhanced CORS
app.use(requestSizeLimiter) // Request size limiting
app.use(generalLimiter) // General rate limiting
app.use(speedLimiter) // Progressive delay
app.use(securityLogger) // Security monitoring

// Body parsing middleware with input sanitization
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))
app.use(sanitizeInput) // Sanitize all inputs

// Compression
app.use(compression())

// Logging
app.use(
  morgan('combined', {
    stream: {
      write: message => logger.info(message.trim())
    }
  })
)

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// API routes
app.use('/api/auth', authRoutes)
app.use('/api/users', userRoutes)
app.use('/api/groups', groupRoutes)
app.use('/api/roles', roleRoutes)
app.use('/api/modules', moduleRoutes)
app.use('/api/permissions', permissionRoutes)
app.use('/api', accessControlRoutes)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  })
})

// Global error handler
app.use(errorHandler)

// Global error handlers
process.on('unhandledRejection', handleUnhandledRejection)
process.on('uncaughtException', handleUncaughtException)

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully')
  process.exit(0)
})

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully')
  process.exit(0)
})

// Start server only in non-serverless environments
if (process.env.NODE_ENV !== 'production' || !process.env.VERCEL) {
  app.listen(PORT, () => {
    logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`)
  })
}

export default app
