{"id": "local-iam-backend-environment", "name": "Local IAM Backend Environment", "values": [{"key": "baseUrl", "value": "http://localhost:3000", "description": "Base URL for the local backend server", "enabled": true}, {"key": "authToken", "value": "", "description": "JWT authentication token (auto-populated after login)", "enabled": true}, {"key": "adminEmail", "value": "<EMAIL>", "description": "Admin user email for login", "enabled": true}, {"key": "adminPassword", "value": "AdminPass123!", "description": "Admin user password for login", "enabled": true}, {"key": "testEmail", "value": "<EMAIL>", "description": "Test user email for registration", "enabled": true}, {"key": "testUsername", "value": "testuser", "description": "Test username for registration", "enabled": true}, {"key": "testPassword", "value": "TestPass123!", "description": "Test user password for registration", "enabled": true}, {"key": "userId", "value": "", "description": "User ID (auto-populated after login or user creation)", "enabled": true}, {"key": "groupId", "value": "", "description": "Group ID (auto-populated after group creation)", "enabled": true}, {"key": "roleId", "value": "", "description": "Role ID (auto-populated after role creation)", "enabled": true}, {"key": "moduleId", "value": "", "description": "Module ID (auto-populated after module creation)", "enabled": true}, {"key": "permissionId", "value": "", "description": "Permission ID (auto-populated after permission creation)", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-01T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}