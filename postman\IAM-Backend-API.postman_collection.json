{"info": {"_postman_id": "iam-backend-api-collection", "name": "IAM Backend API", "description": "Complete API collection for the IAM Backend with role-based access control", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{testEmail}}\",\n  \"username\": \"{{testUsername}}\",\n  \"password\": \"{{testPassword}}\",\n  \"firstName\": \"Test\",\n  \"lastName\": \"User\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.token) {", "        pm.environment.set('authToken', response.data.token);", "        pm.environment.set('userId', response.data.user.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{adminEmail}}\",\n  \"password\": \"{{adminPassword}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"provider\": \"google\",\n  \"providerId\": \"google123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/oauth-login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "oauth-login"]}}}, {"name": "OAuth Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"username\": \"oauthus<PERSON>\",\n  \"firstName\": \"OAuth\",\n  \"lastName\": \"User\",\n  \"provider\": \"google\",\n  \"providerId\": \"google123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/oauth-register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "oauth-register"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}}]}, {"name": "Users", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users?page=1&limit=10&search=&isActive=true", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "isActive", "value": "true"}]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"username\": \"newuser\",\n  \"password\": \"SecurePass123!\",\n  \"firstName\": \"New\",\n  \"lastName\": \"User\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Updated\",\n  \"lastName\": \"User\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}}}, {"name": "Change User Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"newPassword\": \"NewSecurePass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}/password", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}", "password"]}}}, {"name": "Get User by Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{adminEmail}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/by-email", "host": ["{{baseUrl}}"], "path": ["api", "users", "by-email"]}}}]}, {"name": "Groups", "item": [{"name": "Get All Groups", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/groups?page=1&limit=10&search=&isActive=true", "host": ["{{baseUrl}}"], "path": ["api", "groups"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "isActive", "value": "true"}]}}}, {"name": "Get Group by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/groups/{{groupId}}", "host": ["{{baseUrl}}"], "path": ["api", "groups", "{{groupId}}"]}}}, {"name": "Create Group", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data.group.id) {", "        pm.environment.set('groupId', response.data.group.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Group\",\n  \"description\": \"A test group for API testing\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/groups", "host": ["{{baseUrl}}"], "path": ["api", "groups"]}}}, {"name": "Update Group", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Test Group\",\n  \"description\": \"Updated description\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/groups/{{groupId}}", "host": ["{{baseUrl}}"], "path": ["api", "groups", "{{groupId}}"]}}}, {"name": "Delete Group", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/groups/{{groupId}}", "host": ["{{baseUrl}}"], "path": ["api", "groups", "{{groupId}}"]}}}, {"name": "Assign Users to Group", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userIds\": [\"{{userId}}\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/groups/{{groupId}}/users", "host": ["{{baseUrl}}"], "path": ["api", "groups", "{{groupId}}", "users"]}}}, {"name": "Remove User from Group", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/groups/{{groupId}}/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "groups", "{{groupId}}", "users", "{{userId}}"]}}}]}, {"name": "Roles", "item": [{"name": "Get All Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/roles?page=1&limit=10&search=&isActive=true", "host": ["{{baseUrl}}"], "path": ["api", "roles"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "isActive", "value": "true"}]}}}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/roles/{{roleId}}", "host": ["{{baseUrl}}"], "path": ["api", "roles", "{{roleId}}"]}}}, {"name": "Create Role", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data.role.id) {", "        pm.environment.set('roleId', response.data.role.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Role\",\n  \"description\": \"A test role for API testing\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/roles", "host": ["{{baseUrl}}"], "path": ["api", "roles"]}}}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Test Role\",\n  \"description\": \"Updated description\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/roles/{{roleId}}", "host": ["{{baseUrl}}"], "path": ["api", "roles", "{{roleId}}"]}}}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/roles/{{roleId}}", "host": ["{{baseUrl}}"], "path": ["api", "roles", "{{roleId}}"]}}}, {"name": "Assign Roles to Group", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleIds\": [\"{{roleId}}\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/roles/groups/{{groupId}}/roles", "host": ["{{baseUrl}}"], "path": ["api", "roles", "groups", "{{groupId}}", "roles"]}}}, {"name": "Remove Role from Group", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/roles/groups/{{groupId}}/roles/{{roleId}}", "host": ["{{baseUrl}}"], "path": ["api", "roles", "groups", "{{groupId}}", "roles", "{{roleId}}"]}}}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}}