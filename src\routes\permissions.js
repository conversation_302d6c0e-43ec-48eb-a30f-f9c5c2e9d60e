import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { authenticate } from '../middleware/auth.js'
import { checkPermission } from '../middleware/authorization.js'
import prisma from '../utils/database.js'
import logger from '../utils/logger.js'

const router = express.Router()

// Apply authentication to all routes
router.use(authenticate)

// Validation rules
const createPermissionValidation = [
  body('action')
    .isIn(['create', 'read', 'update', 'delete'])
    .withMessage('Action must be one of: create, read, update, delete'),
  body('moduleId').isString().withMessage('Module ID is required'),
  body('description').optional().isLength({ max: 500 }).trim().withMessage('Description must be max 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const updatePermissionValidation = [
  param('id').isString().withMessage('Permission ID must be a string'),
  body('action')
    .optional()
    .isIn(['create', 'read', 'update', 'delete'])
    .withMessage('Action must be one of: create, read, update, delete'),
  body('description').optional().isLength({ max: 500 }).trim().withMessage('Description must be max 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const assignPermissionValidation = [
  param('roleId').isString().withMessage('Role ID must be a string'),
  body('permissionIds').isArray({ min: 1 }).withMessage('permissionIds must be a non-empty array'),
  body('permissionIds.*').isString().withMessage('Each permission ID must be a string')
]

/**
 * @route   GET /api/permissions
 * @desc    Get all permissions with pagination and filtering
 * @access  Private (requires Permissions:read permission)
 */
router.get('/', checkPermission('Permissions', 'read'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const search = req.query.search || ''
    const isActive = req.query.isActive
    const moduleId = req.query.moduleId
    const action = req.query.action

    const skip = (page - 1) * limit

    // Build where clause
    const where = {}

    if (search) {
      where.OR = [
        { action: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { module: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    if (moduleId) {
      where.moduleId = moduleId
    }

    if (action) {
      where.action = action
    }

    const [permissions, total] = await Promise.all([
      prisma.permission.findMany({
        where,
        include: {
          module: {
            select: {
              id: true,
              name: true,
              description: true
            }
          },
          roles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true
                }
              }
            }
          },
          _count: {
            select: {
              roles: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: [{ module: { name: 'asc' } }, { action: 'asc' }]
      }),
      prisma.permission.count({ where })
    ])

    res.json({
      success: true,
      data: {
        permissions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Get permissions error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get permissions'
    })
  }
})

/**
 * @route   GET /api/permissions/:id
 * @desc    Get permission by ID
 * @access  Private (requires Permissions:read permission)
 */
router.get('/:id', checkPermission('Permissions', 'read'), async (req, res) => {
  try {
    const { id } = req.params

    const permission = await prisma.permission.findUnique({
      where: { id },
      include: {
        module: {
          select: {
            id: true,
            name: true,
            description: true,
            isActive: true
          }
        },
        roles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true,
                isActive: true
              }
            }
          }
        }
      }
    })

    if (!permission) {
      return res.status(404).json({
        success: false,
        error: 'Permission not found'
      })
    }

    res.json({
      success: true,
      data: { permission }
    })
  } catch (error) {
    logger.error('Get permission error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get permission'
    })
  }
})

/**
 * @route   POST /api/permissions
 * @desc    Create new permission
 * @access  Private (requires Permissions:create permission)
 */
router.post('/', checkPermission('Permissions', 'create'), createPermissionValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { action, moduleId, description, isActive = true } = req.body

    // Check if module exists
    const module = await prisma.module.findUnique({
      where: { id: moduleId }
    })

    if (!module) {
      return res.status(400).json({
        success: false,
        error: 'Module not found'
      })
    }

    // Check if permission already exists for this module and action
    const existingPermission = await prisma.permission.findUnique({
      where: {
        moduleId_action: {
          moduleId,
          action
        }
      }
    })

    if (existingPermission) {
      return res.status(400).json({
        success: false,
        error: 'Permission with this action already exists for this module'
      })
    }

    // Create permission
    const permission = await prisma.permission.create({
      data: {
        action,
        moduleId,
        description: description || `${action.charAt(0).toUpperCase() + action.slice(1)} ${module.name.toLowerCase()}`,
        isActive
      },
      include: {
        module: {
          select: {
            id: true,
            name: true,
            description: true
          }
        }
      }
    })

    logger.info(`Permission created by ${req.user.email}: ${module.name}:${action}`)

    res.status(201).json({
      success: true,
      message: 'Permission created successfully',
      data: { permission }
    })
  } catch (error) {
    logger.error('Create permission error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to create permission'
    })
  }
})

/**
 * @route   PUT /api/permissions/:id
 * @desc    Update permission
 * @access  Private (requires Permissions:update permission)
 */
router.put('/:id', checkPermission('Permissions', 'update'), updatePermissionValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { id } = req.params
    const { action, description, isActive } = req.body

    // Check if permission exists
    const existingPermission = await prisma.permission.findUnique({
      where: { id },
      include: {
        module: true
      }
    })

    if (!existingPermission) {
      return res.status(404).json({
        success: false,
        error: 'Permission not found'
      })
    }

    // Check for action conflicts if action is being updated
    if (action && action !== existingPermission.action) {
      const conflictPermission = await prisma.permission.findUnique({
        where: {
          moduleId_action: {
            moduleId: existingPermission.moduleId,
            action
          }
        }
      })

      if (conflictPermission) {
        return res.status(400).json({
          success: false,
          error: 'Permission with this action already exists for this module'
        })
      }
    }

    // Update permission
    const updateData = {}
    if (action !== undefined) updateData.action = action
    if (description !== undefined) updateData.description = description
    if (isActive !== undefined) updateData.isActive = isActive

    const permission = await prisma.permission.update({
      where: { id },
      data: updateData,
      include: {
        module: {
          select: {
            id: true,
            name: true,
            description: true
          }
        }
      }
    })

    logger.info(`Permission updated by ${req.user.email}: ${permission.module.name}:${permission.action}`)

    res.json({
      success: true,
      message: 'Permission updated successfully',
      data: { permission }
    })
  } catch (error) {
    logger.error('Update permission error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to update permission'
    })
  }
})

/**
 * @route   DELETE /api/permissions/:id
 * @desc    Delete permission
 * @access  Private (requires Permissions:delete permission)
 */
router.delete('/:id', checkPermission('Permissions', 'delete'), async (req, res) => {
  try {
    const { id } = req.params

    // Check if permission exists
    const existingPermission = await prisma.permission.findUnique({
      where: { id },
      include: {
        module: true,
        _count: {
          select: {
            roles: true
          }
        }
      }
    })

    if (!existingPermission) {
      return res.status(404).json({
        success: false,
        error: 'Permission not found'
      })
    }

    // Check if it's a system permission (prevent deletion of critical permissions)
    const systemModules = ['Users', 'Groups', 'Roles', 'Modules', 'Permissions']
    if (systemModules.includes(existingPermission.module.name)) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete system permissions'
      })
    }

    // Delete permission (cascade will handle related records)
    await prisma.permission.delete({
      where: { id }
    })

    logger.info(
      `Permission deleted by ${req.user.email}: ${existingPermission.module.name}:${existingPermission.action}`
    )

    res.json({
      success: true,
      message: 'Permission deleted successfully'
    })
  } catch (error) {
    logger.error('Delete permission error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to delete permission'
    })
  }
})

/**
 * @route   POST /api/roles/:roleId/permissions
 * @desc    Assign permissions to role
 * @access  Private (requires Roles:update permission)
 */
router.post(
  '/roles/:roleId/permissions',
  checkPermission('Roles', 'update'),
  assignPermissionValidation,
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { roleId } = req.params
      const { permissionIds } = req.body

      // Check if role exists
      const role = await prisma.role.findUnique({
        where: { id: roleId }
      })

      if (!role) {
        return res.status(404).json({
          success: false,
          error: 'Role not found'
        })
      }

      // Check if all permissions exist
      const permissions = await prisma.permission.findMany({
        where: {
          id: { in: permissionIds },
          isActive: true
        }
      })

      if (permissions.length !== permissionIds.length) {
        return res.status(400).json({
          success: false,
          error: 'One or more permissions not found or inactive'
        })
      }

      // Get existing assignments
      const existingAssignments = await prisma.rolePermission.findMany({
        where: {
          roleId,
          permissionId: { in: permissionIds }
        }
      })

      const existingPermissionIds = existingAssignments.map(a => a.permissionId)
      const newPermissionIds = permissionIds.filter(id => !existingPermissionIds.includes(id))

      // Create new assignments
      if (newPermissionIds.length > 0) {
        await prisma.rolePermission.createMany({
          data: newPermissionIds.map(permissionId => ({
            permissionId,
            roleId
          }))
        })
      }

      logger.info(
        `Permissions assigned to role ${role.name} by ${req.user.email}: ${newPermissionIds.length} new, ${existingPermissionIds.length} existing`
      )

      res.json({
        success: true,
        message: `${newPermissionIds.length} permissions assigned to role successfully`,
        data: {
          newAssignments: newPermissionIds.length,
          existingAssignments: existingPermissionIds.length
        }
      })
    } catch (error) {
      logger.error('Assign permissions to role error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to assign permissions to role'
      })
    }
  }
)

/**
 * @route   DELETE /api/roles/:roleId/permissions/:permissionId
 * @desc    Remove permission from role
 * @access  Private (requires Roles:update permission)
 */
router.delete('/roles/:roleId/permissions/:permissionId', checkPermission('Roles', 'update'), async (req, res) => {
  try {
    const { roleId, permissionId } = req.params

    // Check if assignment exists
    const assignment = await prisma.rolePermission.findUnique({
      where: {
        roleId_permissionId: {
          roleId,
          permissionId
        }
      },
      include: {
        role: { select: { name: true } },
        permission: {
          select: {
            action: true,
            module: { select: { name: true } }
          }
        }
      }
    })

    if (!assignment) {
      return res.status(404).json({
        success: false,
        error: 'Permission is not assigned to this role'
      })
    }

    // Prevent removing critical permissions from admin role
    if (assignment.role.name === 'Admin') {
      return res.status(400).json({
        success: false,
        error: 'Cannot remove permissions from Admin role'
      })
    }

    // Remove assignment
    await prisma.rolePermission.delete({
      where: {
        roleId_permissionId: {
          roleId,
          permissionId
        }
      }
    })

    logger.info(
      `Permission ${assignment.permission.module.name}:${assignment.permission.action} removed from role ${assignment.role.name} by ${req.user.email}`
    )

    res.json({
      success: true,
      message: 'Permission removed from role successfully'
    })
  } catch (error) {
    logger.error('Remove permission from role error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to remove permission from role'
    })
  }
})

export default router
