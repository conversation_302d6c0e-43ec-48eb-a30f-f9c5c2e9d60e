import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { authenticate } from '../middleware/auth.js'
import { checkPermission } from '../middleware/authorization.js'
import prisma from '../utils/database.js'
import logger from '../utils/logger.js'

const router = express.Router()

// Apply authentication to all routes
router.use(authenticate)

// Validation rules
const createGroupValidation = [
  body('name').isLength({ min: 1, max: 100 }).trim().withMessage('Group name must be 1-100 characters'),
  body('description').optional().isLength({ max: 500 }).trim().withMessage('Description must be max 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const updateGroupValidation = [
  param('id').isString().withMessage('Group ID must be a string'),
  body('name').optional().isLength({ min: 1, max: 100 }).trim().withMessage('Group name must be 1-100 characters'),
  body('description').optional().isLength({ max: 500 }).trim().withMessage('Description must be max 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const assignUserValidation = [
  param('groupId').isString().withMessage('Group ID must be a string'),
  body('userIds').isArray({ min: 1 }).withMessage('userIds must be a non-empty array'),
  body('userIds.*').isString().withMessage('Each user ID must be a string')
]

/**
 * @route   GET /api/groups
 * @desc    Get all groups with pagination and filtering
 * @access  Private (requires Groups:read permission)
 */
router.get('/', checkPermission('Groups', 'read'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const search = req.query.search || ''
    const isActive = req.query.isActive

    const skip = (page - 1) * limit

    // Build where clause
    const where = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    const [groups, total] = await Promise.all([
      prisma.group.findMany({
        where,
        include: {
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  username: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          },
          roles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true
                }
              }
            }
          },
          _count: {
            select: {
              members: true,
              roles: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.group.count({ where })
    ])

    res.json({
      success: true,
      data: {
        groups,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Get groups error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get groups'
    })
  }
})

/**
 * @route   GET /api/groups/:id
 * @desc    Get group by ID
 * @access  Private (requires Groups:read permission)
 */
router.get('/:id', checkPermission('Groups', 'read'), async (req, res) => {
  try {
    const { id } = req.params

    const group = await prisma.group.findUnique({
      where: { id },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                username: true,
                firstName: true,
                lastName: true,
                isActive: true
              }
            }
          }
        },
        roles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true,
                isActive: true
              }
            }
          }
        }
      }
    })

    if (!group) {
      return res.status(404).json({
        success: false,
        error: 'Group not found'
      })
    }

    res.json({
      success: true,
      data: { group }
    })
  } catch (error) {
    logger.error('Get group error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get group'
    })
  }
})

/**
 * @route   POST /api/groups
 * @desc    Create new group
 * @access  Private (requires Groups:create permission)
 */
router.post('/', checkPermission('Groups', 'create'), createGroupValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { name, description, isActive = true } = req.body

    // Check if group already exists
    const existingGroup = await prisma.group.findUnique({
      where: { name }
    })

    if (existingGroup) {
      return res.status(400).json({
        success: false,
        error: 'Group with this name already exists'
      })
    }

    // Create group
    const group = await prisma.group.create({
      data: {
        name,
        description: description || null,
        isActive
      }
    })

    logger.info(`Group created by ${req.user.email}: ${group.name}`)

    res.status(201).json({
      success: true,
      message: 'Group created successfully',
      data: { group }
    })
  } catch (error) {
    logger.error('Create group error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to create group'
    })
  }
})

/**
 * @route   PUT /api/groups/:id
 * @desc    Update group
 * @access  Private (requires Groups:update permission)
 */
router.put('/:id', checkPermission('Groups', 'update'), updateGroupValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { id } = req.params
    const { name, description, isActive } = req.body

    // Check if group exists
    const existingGroup = await prisma.group.findUnique({
      where: { id }
    })

    if (!existingGroup) {
      return res.status(404).json({
        success: false,
        error: 'Group not found'
      })
    }

    // Check for name conflicts
    if (name) {
      const conflictGroup = await prisma.group.findFirst({
        where: {
          AND: [{ id: { not: id } }, { name }]
        }
      })

      if (conflictGroup) {
        return res.status(400).json({
          success: false,
          error: 'Group with this name already exists'
        })
      }
    }

    // Update group
    const updateData = {}
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (isActive !== undefined) updateData.isActive = isActive

    const group = await prisma.group.update({
      where: { id },
      data: updateData
    })

    logger.info(`Group updated by ${req.user.email}: ${group.name}`)

    res.json({
      success: true,
      message: 'Group updated successfully',
      data: { group }
    })
  } catch (error) {
    logger.error('Update group error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to update group'
    })
  }
})

/**
 * @route   DELETE /api/groups/:id
 * @desc    Delete group
 * @access  Private (requires Groups:delete permission)
 */
router.delete('/:id', checkPermission('Groups', 'delete'), async (req, res) => {
  try {
    const { id } = req.params

    // Check if group exists
    const existingGroup = await prisma.group.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            members: true,
            roles: true
          }
        }
      }
    })

    if (!existingGroup) {
      return res.status(404).json({
        success: false,
        error: 'Group not found'
      })
    }

    // Check if it's a system group (prevent deletion of critical groups)
    const systemGroups = ['Administrators', 'Users']
    if (systemGroups.includes(existingGroup.name)) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete system groups'
      })
    }

    // Delete group (cascade will handle related records)
    await prisma.group.delete({
      where: { id }
    })

    logger.info(`Group deleted by ${req.user.email}: ${existingGroup.name}`)

    res.json({
      success: true,
      message: 'Group deleted successfully'
    })
  } catch (error) {
    logger.error('Delete group error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to delete group'
    })
  }
})

/**
 * @route   POST /api/groups/:groupId/users
 * @desc    Assign users to group
 * @access  Private (requires Groups:update permission)
 */
router.post('/:groupId/users', checkPermission('Groups', 'update'), assignUserValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { groupId } = req.params
    const { userIds } = req.body

    // Check if group exists
    const group = await prisma.group.findUnique({
      where: { id: groupId }
    })

    if (!group) {
      return res.status(404).json({
        success: false,
        error: 'Group not found'
      })
    }

    // Check if all users exist
    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds },
        isActive: true
      }
    })

    if (users.length !== userIds.length) {
      return res.status(400).json({
        success: false,
        error: 'One or more users not found or inactive'
      })
    }

    // Get existing memberships
    const existingMemberships = await prisma.userGroup.findMany({
      where: {
        groupId,
        userId: { in: userIds }
      }
    })

    const existingUserIds = existingMemberships.map(m => m.userId)
    const newUserIds = userIds.filter(id => !existingUserIds.includes(id))

    // Create new memberships
    if (newUserIds.length > 0) {
      await prisma.userGroup.createMany({
        data: newUserIds.map(userId => ({
          userId,
          groupId
        }))
      })
    }

    logger.info(
      `Users assigned to group ${group.name} by ${req.user.email}: ${newUserIds.length} new, ${existingUserIds.length} existing`
    )

    res.json({
      success: true,
      message: `${newUserIds.length} users assigned to group successfully`,
      data: {
        newAssignments: newUserIds.length,
        existingAssignments: existingUserIds.length
      }
    })
  } catch (error) {
    logger.error('Assign users to group error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to assign users to group'
    })
  }
})

/**
 * @route   DELETE /api/groups/:groupId/users/:userId
 * @desc    Remove user from group
 * @access  Private (requires Groups:update permission)
 */
router.delete('/:groupId/users/:userId', checkPermission('Groups', 'update'), async (req, res) => {
  try {
    const { groupId, userId } = req.params

    // Check if membership exists
    const membership = await prisma.userGroup.findUnique({
      where: {
        userId_groupId: {
          userId,
          groupId
        }
      },
      include: {
        user: { select: { email: true } },
        group: { select: { name: true } }
      }
    })

    if (!membership) {
      return res.status(404).json({
        success: false,
        error: 'User is not a member of this group'
      })
    }

    // Prevent removing user from critical groups if they're the last admin
    if (membership.group.name === 'Administrators') {
      const adminCount = await prisma.userGroup.count({
        where: { groupId }
      })

      if (adminCount <= 1) {
        return res.status(400).json({
          success: false,
          error: 'Cannot remove the last administrator from the Administrators group'
        })
      }
    }

    // Remove membership
    await prisma.userGroup.delete({
      where: {
        userId_groupId: {
          userId,
          groupId
        }
      }
    })

    logger.info(`User ${membership.user.email} removed from group ${membership.group.name} by ${req.user.email}`)

    res.json({
      success: true,
      message: 'User removed from group successfully'
    })
  } catch (error) {
    logger.error('Remove user from group error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to remove user from group'
    })
  }
})

export default router
