import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { authenticate } from '../middleware/auth.js'
import { checkPermission } from '../middleware/authorization.js'
import prisma from '../utils/database.js'
import logger from '../utils/logger.js'

const router = express.Router()

// Apply authentication to all routes
router.use(authenticate)

// Validation rules
const createRoleValidation = [
  body('name').isLength({ min: 1, max: 100 }).trim().withMessage('Role name must be 1-100 characters'),
  body('description').optional().isLength({ max: 500 }).trim().withMessage('Description must be max 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const updateRoleValidation = [
  param('id').isString().withMessage('Role ID must be a string'),
  body('name').optional().isLength({ min: 1, max: 100 }).trim().withMessage('Role name must be 1-100 characters'),
  body('description').optional().isLength({ max: 500 }).trim().withMessage('Description must be max 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const assignRoleValidation = [
  param('groupId').isString().withMessage('Group ID must be a string'),
  body('roleIds').isArray({ min: 1 }).withMessage('roleIds must be a non-empty array'),
  body('roleIds.*').isString().withMessage('Each role ID must be a string')
]

/**
 * @route   GET /api/roles
 * @desc    Get all roles with pagination and filtering
 * @access  Private (requires Roles:read permission)
 */
router.get('/', checkPermission('Roles', 'read'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const search = req.query.search || ''
    const isActive = req.query.isActive

    const skip = (page - 1) * limit

    // Build where clause
    const where = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    const [roles, total] = await Promise.all([
      prisma.role.findMany({
        where,
        include: {
          groups: {
            include: {
              group: {
                select: {
                  id: true,
                  name: true,
                  description: true
                }
              }
            }
          },
          permissions: {
            include: {
              permission: {
                include: {
                  module: {
                    select: {
                      id: true,
                      name: true,
                      description: true
                    }
                  }
                }
              }
            }
          },
          _count: {
            select: {
              groups: true,
              permissions: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.role.count({ where })
    ])

    res.json({
      success: true,
      data: {
        roles,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Get roles error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get roles'
    })
  }
})

/**
 * @route   GET /api/roles/:id
 * @desc    Get role by ID
 * @access  Private (requires Roles:read permission)
 */
router.get('/:id', checkPermission('Roles', 'read'), async (req, res) => {
  try {
    const { id } = req.params

    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        groups: {
          include: {
            group: {
              select: {
                id: true,
                name: true,
                description: true,
                isActive: true
              }
            }
          }
        },
        permissions: {
          include: {
            permission: {
              include: {
                module: {
                  select: {
                    id: true,
                    name: true,
                    description: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      })
    }

    res.json({
      success: true,
      data: { role }
    })
  } catch (error) {
    logger.error('Get role error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get role'
    })
  }
})

/**
 * @route   POST /api/roles
 * @desc    Create new role
 * @access  Private (requires Roles:create permission)
 */
router.post('/', checkPermission('Roles', 'create'), createRoleValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { name, description, isActive = true } = req.body

    // Check if role already exists
    const existingRole = await prisma.role.findUnique({
      where: { name }
    })

    if (existingRole) {
      return res.status(400).json({
        success: false,
        error: 'Role with this name already exists'
      })
    }

    // Create role
    const role = await prisma.role.create({
      data: {
        name,
        description: description || null,
        isActive
      }
    })

    logger.info(`Role created by ${req.user.email}: ${role.name}`)

    res.status(201).json({
      success: true,
      message: 'Role created successfully',
      data: { role }
    })
  } catch (error) {
    logger.error('Create role error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to create role'
    })
  }
})

/**
 * @route   PUT /api/roles/:id
 * @desc    Update role
 * @access  Private (requires Roles:update permission)
 */
router.put('/:id', checkPermission('Roles', 'update'), updateRoleValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { id } = req.params
    const { name, description, isActive } = req.body

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id }
    })

    if (!existingRole) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      })
    }

    // Check for name conflicts
    if (name) {
      const conflictRole = await prisma.role.findFirst({
        where: {
          AND: [{ id: { not: id } }, { name }]
        }
      })

      if (conflictRole) {
        return res.status(400).json({
          success: false,
          error: 'Role with this name already exists'
        })
      }
    }

    // Update role
    const updateData = {}
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (isActive !== undefined) updateData.isActive = isActive

    const role = await prisma.role.update({
      where: { id },
      data: updateData
    })

    logger.info(`Role updated by ${req.user.email}: ${role.name}`)

    res.json({
      success: true,
      message: 'Role updated successfully',
      data: { role }
    })
  } catch (error) {
    logger.error('Update role error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to update role'
    })
  }
})

/**
 * @route   DELETE /api/roles/:id
 * @desc    Delete role
 * @access  Private (requires Roles:delete permission)
 */
router.delete('/:id', checkPermission('Roles', 'delete'), async (req, res) => {
  try {
    const { id } = req.params

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            groups: true,
            permissions: true
          }
        }
      }
    })

    if (!existingRole) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      })
    }

    // Check if it's a system role (prevent deletion of critical roles)
    const systemRoles = ['Admin', 'User']
    if (systemRoles.includes(existingRole.name)) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete system roles'
      })
    }

    // Delete role (cascade will handle related records)
    await prisma.role.delete({
      where: { id }
    })

    logger.info(`Role deleted by ${req.user.email}: ${existingRole.name}`)

    res.json({
      success: true,
      message: 'Role deleted successfully'
    })
  } catch (error) {
    logger.error('Delete role error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to delete role'
    })
  }
})

/**
 * @route   POST /api/groups/:groupId/roles
 * @desc    Assign roles to group
 * @access  Private (requires Groups:update permission)
 */
router.post('/groups/:groupId/roles', checkPermission('Groups', 'update'), assignRoleValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { groupId } = req.params
    const { roleIds } = req.body

    // Check if group exists
    const group = await prisma.group.findUnique({
      where: { id: groupId }
    })

    if (!group) {
      return res.status(404).json({
        success: false,
        error: 'Group not found'
      })
    }

    // Check if all roles exist
    const roles = await prisma.role.findMany({
      where: {
        id: { in: roleIds },
        isActive: true
      }
    })

    if (roles.length !== roleIds.length) {
      return res.status(400).json({
        success: false,
        error: 'One or more roles not found or inactive'
      })
    }

    // Get existing assignments
    const existingAssignments = await prisma.groupRole.findMany({
      where: {
        groupId,
        roleId: { in: roleIds }
      }
    })

    const existingRoleIds = existingAssignments.map(a => a.roleId)
    const newRoleIds = roleIds.filter(id => !existingRoleIds.includes(id))

    // Create new assignments
    if (newRoleIds.length > 0) {
      await prisma.groupRole.createMany({
        data: newRoleIds.map(roleId => ({
          roleId,
          groupId
        }))
      })
    }

    logger.info(
      `Roles assigned to group ${group.name} by ${req.user.email}: ${newRoleIds.length} new, ${existingRoleIds.length} existing`
    )

    res.json({
      success: true,
      message: `${newRoleIds.length} roles assigned to group successfully`,
      data: {
        newAssignments: newRoleIds.length,
        existingAssignments: existingRoleIds.length
      }
    })
  } catch (error) {
    logger.error('Assign roles to group error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to assign roles to group'
    })
  }
})

/**
 * @route   DELETE /api/groups/:groupId/roles/:roleId
 * @desc    Remove role from group
 * @access  Private (requires Groups:update permission)
 */
router.delete('/groups/:groupId/roles/:roleId', checkPermission('Groups', 'update'), async (req, res) => {
  try {
    const { groupId, roleId } = req.params

    // Check if assignment exists
    const assignment = await prisma.groupRole.findUnique({
      where: {
        groupId_roleId: {
          groupId,
          roleId
        }
      },
      include: {
        group: { select: { name: true } },
        role: { select: { name: true } }
      }
    })

    if (!assignment) {
      return res.status(404).json({
        success: false,
        error: 'Role is not assigned to this group'
      })
    }

    // Prevent removing critical role assignments
    if (assignment.group.name === 'Administrators' && assignment.role.name === 'Admin') {
      const adminRoleCount = await prisma.groupRole.count({
        where: {
          groupId,
          role: { name: 'Admin' }
        }
      })

      if (adminRoleCount <= 1) {
        return res.status(400).json({
          success: false,
          error: 'Cannot remove the Admin role from the Administrators group'
        })
      }
    }

    // Remove assignment
    await prisma.groupRole.delete({
      where: {
        groupId_roleId: {
          groupId,
          roleId
        }
      }
    })

    logger.info(`Role ${assignment.role.name} removed from group ${assignment.group.name} by ${req.user.email}`)

    res.json({
      success: true,
      message: 'Role removed from group successfully'
    })
  } catch (error) {
    logger.error('Remove role from group error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to remove role from group'
    })
  }
})

export default router
