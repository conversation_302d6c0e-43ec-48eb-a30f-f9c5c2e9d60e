// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  groupMemberships UserGroup[]

  @@map("users")
}

model Group {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  members UserGroup[]
  roles   GroupRole[]

  @@map("groups")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  groups      GroupRole[]
  permissions RolePermission[]

  @@map("roles")
}

model Module {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  permissions Permission[]

  @@map("modules")
}

model Permission {
  id          String   @id @default(cuid())
  action      String   // create, read, update, delete
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Foreign keys
  moduleId String
  module   Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  // Relationships
  roles RolePermission[]

  @@unique([moduleId, action])
  @@map("permissions")
}

// Junction tables for many-to-many relationships

model UserGroup {
  id       String @id @default(cuid())
  userId   String
  groupId  String
  joinedAt DateTime @default(now())

  // Foreign keys
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)

  @@unique([userId, groupId])
  @@map("user_groups")
}

model GroupRole {
  id         String   @id @default(cuid())
  groupId    String
  roleId     String
  assignedAt DateTime @default(now())

  // Foreign keys
  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)
  role  Role  @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([groupId, roleId])
  @@map("group_roles")
}

model RolePermission {
  id           String   @id @default(cuid())
  roleId       String
  permissionId String
  assignedAt   DateTime @default(now())

  // Foreign keys
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}
