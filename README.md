# IAM Backend Server

A comprehensive Identity and Access Management (IAM) backend system built with Node.js, Express, and PostgreSQL. This production-ready backend provides full user authentication, authorization, and permission management capabilities.

## 🚀 Features

### Core IAM Functionality
- **User Authentication**: JWT-based secure authentication system
- **User Authorization**: Role-based access control (RBAC) with permission inheritance
- **User Management**: Complete CRUD operations for user accounts
- **Group Management**: Organize users into groups for easier permission management
- **Role Management**: Define roles with specific permissions
- **Module Management**: Organize permissions by business modules
- **Permission Management**: Granular permission control (create, read, update, delete)
- **Access Control**: Real-time permission checking and simulation

### Security Features
- **JWT Authentication**: Secure token-based authentication
- **Password Security**: bcrypt hashing with salt
- **Rate Limiting**: Multiple rate limits for different endpoint types
- **Input Validation**: Comprehensive validation and sanitization
- **CORS Protection**: Configurable cross-origin resource sharing
- **Security Headers**: Helmet.js security headers
- **XSS Protection**: Input sanitization and escape
- **SQL Injection Protection**: Prisma ORM parameterized queries
- **Request Size Limiting**: Prevent large payload attacks
- **IP Filtering**: Whitelist/blacklist support

### Production Features
- **Error Handling**: Comprehensive error handling with custom error classes
- **Logging**: Winston-based structured logging
- **Database**: PostgreSQL with Prisma ORM
- **Validation**: express-validator for input validation
- **API Documentation**: Complete API documentation
- **Testing**: Basic API testing script
- **Environment Configuration**: Flexible environment-based configuration

## 📋 Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/iam_db"
   
   # JWT
   JWT_SECRET="your-super-secret-jwt-key"
   JWT_EXPIRES_IN="7d"
   
   # Server
   PORT=3001
   NODE_ENV="development"
   
   # Rate Limiting
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   
   # Security
   ALLOWED_ORIGINS="http://localhost:3000"
   MAX_REQUEST_SIZE=10485760
   
   # Logging
   LOG_LEVEL="info"
   ```

4. **Set up the database**
   ```bash
   # Generate Prisma client
   npx prisma generate
   
   # Push database schema
   npx prisma db push
   
   # Seed the database with initial data
   npx prisma db seed
   ```

5. **Start the server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## 🧪 Testing

### Health Check
```bash
curl http://localhost:3001/health
```

### Run API Tests
```bash
node test-api.js
```

### Manual Testing
Use the provided API documentation and tools like Postman or Insomnia to test endpoints.

## 📚 API Documentation

See [API_DOCUMENTATION.md](./API_DOCUMENTATION.md) for complete API reference.

### Quick Start Examples

**Register a new user:**
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "SecurePass123!",
    "firstName": "Test",
    "lastName": "User"
  }'
```

**Login:**
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!"
  }'
```

**Get user permissions:**
```bash
curl -X GET http://localhost:3001/api/me/permissions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🏗️ Architecture

### Database Schema
- **Users**: User accounts with authentication credentials
- **Groups**: User groups for organizing users
- **Roles**: Permission containers assigned to groups
- **Modules**: Business areas that define permission contexts
- **Permissions**: Granular access rights (action + module)
- **Relationships**: Many-to-many relationships between entities

### Permission Inheritance
Permissions are inherited through the following path:
```
User → Groups → Roles → Permissions
```

Users get permissions only through their group memberships. Groups can have multiple roles, and roles can have multiple permissions.

### Middleware Stack
1. **Security Middleware**: IP filtering, headers, CORS, CSP
2. **Rate Limiting**: Different limits for different endpoint types
3. **Input Sanitization**: XSS and injection prevention
4. **Authentication**: JWT token verification
5. **Authorization**: Permission checking
6. **Validation**: Input validation and sanitization
7. **Error Handling**: Comprehensive error processing

## 📁 Project Structure

```
backend/
├── src/
│   ├── middleware/          # Express middleware
│   │   ├── auth.js         # Authentication middleware
│   │   ├── authorize.js    # Authorization middleware
│   │   ├── errorHandler.js # Error handling
│   │   ├── rateLimiter.js  # Rate limiting
│   │   ├── security.js     # Security middleware
│   │   └── validation.js   # Input validation
│   ├── routes/             # API routes
│   │   ├── auth.js         # Authentication routes
│   │   ├── users.js        # User management
│   │   ├── groups.js       # Group management
│   │   ├── roles.js        # Role management
│   │   ├── modules.js      # Module management
│   │   ├── permissions.js  # Permission management
│   │   └── accessControl.js # Access control
│   ├── utils/              # Utility functions
│   │   ├── auth.js         # Auth utilities
│   │   ├── database.js     # Database connection
│   │   └── logger.js       # Logging utilities
│   └── server.js           # Main server file
├── prisma/
│   ├── schema.prisma       # Database schema
│   └── seed.js            # Database seeding
├── .env.example           # Environment template
├── package.json           # Dependencies
├── API_DOCUMENTATION.md   # Complete API docs
├── test-api.js           # API testing script
└── README.md             # This file
```

## 🔐 Default System Data

The system comes pre-configured with:

### Default Admin User
- **Email**: <EMAIL>
- **Password**: Admin123!
- **Username**: admin

### Default Groups
- **Administrators**: System admin group
- **Users**: Default user group

### Default Roles
- **Admin**: Full system access
- **User**: Basic user access

### Default Modules
- Users, Groups, Roles, Modules, Permissions

## 🚀 Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Use a strong `JWT_SECRET`
3. Configure proper database credentials
4. Set up proper CORS origins
5. Configure rate limiting for your needs

### Security Checklist
- [ ] Strong JWT secret (32+ characters)
- [ ] HTTPS in production
- [ ] Proper CORS configuration
- [ ] Rate limiting configured
- [ ] Input validation enabled
- [ ] Security headers configured
- [ ] Database credentials secured
- [ ] Logging configured
- [ ] Error handling in place

### Performance Considerations
- Database connection pooling
- Proper indexing on frequently queried fields
- Rate limiting to prevent abuse
- Request size limiting
- Efficient pagination

## 🤝 Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure all tests pass
5. Follow security best practices

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the API documentation
2. Review the test examples
3. Check the logs for error details
4. Ensure proper environment configuration

## 🔄 Version History

- **v1.0.0**: Initial release with full IAM functionality
  - User authentication and authorization
  - Complete RBAC system
  - Security hardening
  - Production-ready features
