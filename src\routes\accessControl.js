import express from 'express'
import { body, validationResult } from 'express-validator'
import { authenticate } from '../middleware/auth.js'
import { getUserPermissions, hasPermission } from '../middleware/authorization.js'
import prisma from '../utils/database.js'
import logger from '../utils/logger.js'

const router = express.Router()

// Apply authentication to all routes
router.use(authenticate)

// Validation rules
const simulateActionValidation = [
  body('userId').optional().isString().withMessage('User ID must be a string'),
  body('module').isString().isLength({ min: 1 }).withMessage('Module is required'),
  body('action')
    .isIn(['create', 'read', 'update', 'delete'])
    .withMessage('Action must be one of: create, read, update, delete')
]

/**
 * @route   GET /api/me/permissions
 * @desc    Get current user's inherited permissions
 * @access  Private
 */
router.get('/me/permissions', async (req, res) => {
  try {
    const permissions = await getUserPermissions(req.user.id)

    // Group permissions by module for better organization
    const permissionsByModule = permissions.reduce((acc, permission) => {
      const moduleName = permission.module
      if (!acc[moduleName]) {
        acc[moduleName] = []
      }
      acc[moduleName].push({
        id: permission.id,
        action: permission.action,
        description: permission.description
      })
      return acc
    }, {})

    // Get user's groups and roles for context
    const userWithGroups = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        groupMemberships: {
          include: {
            group: {
              include: {
                roles: {
                  include: {
                    role: {
                      select: {
                        id: true,
                        name: true,
                        description: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    const groups = userWithGroups.groupMemberships.map(membership => ({
      id: membership.group.id,
      name: membership.group.name,
      description: membership.group.description,
      joinedAt: membership.joinedAt,
      roles: membership.group.roles.map(groupRole => groupRole.role)
    }))

    res.json({
      success: true,
      data: {
        user: {
          id: req.user.id,
          email: req.user.email,
          username: req.user.username
        },
        groups,
        permissions: {
          total: permissions.length,
          byModule: permissionsByModule,
          list: permissions
        }
      }
    })
  } catch (error) {
    logger.error('Get user permissions error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get user permissions'
    })
  }
})

/**
 * @route   POST /api/simulate-action
 * @desc    Test a user's ability to perform an action on a module
 * @access  Private
 */
router.post('/simulate-action', simulateActionValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { userId, module, action } = req.body
    const targetUserId = userId || req.user.id

    // If simulating for another user, check if current user has permission
    if (userId && userId !== req.user.id) {
      const canSimulate = await hasPermission(req.user.id, 'Users', 'read')
      if (!canSimulate) {
        return res.status(403).json({
          success: false,
          error: 'Access denied. Cannot simulate actions for other users.'
        })
      }
    }

    // Check if target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: targetUserId },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    })

    if (!targetUser) {
      return res.status(404).json({
        success: false,
        error: 'Target user not found'
      })
    }

    if (!targetUser.isActive) {
      return res.status(400).json({
        success: false,
        error: 'Target user is inactive'
      })
    }

    // Check if module exists
    const moduleExists = await prisma.module.findFirst({
      where: {
        name: { equals: module, mode: 'insensitive' },
        isActive: true
      }
    })

    if (!moduleExists) {
      return res.status(400).json({
        success: false,
        error: 'Module not found or inactive'
      })
    }

    // Simulate the action
    const hasAccess = await hasPermission(targetUserId, module, action)

    // Get detailed permission information
    const userPermissions = await getUserPermissions(targetUserId)
    const relevantPermission = userPermissions.find(
      p => p.module.toLowerCase() === module.toLowerCase() && p.action.toLowerCase() === action.toLowerCase()
    )

    // Get user's groups and roles that grant this permission (if any)
    let grantingPath = null
    if (hasAccess && relevantPermission) {
      const userWithDetails = await prisma.user.findUnique({
        where: { id: targetUserId },
        include: {
          groupMemberships: {
            include: {
              group: {
                include: {
                  roles: {
                    include: {
                      role: {
                        include: {
                          permissions: {
                            where: {
                              permission: {
                                moduleId: moduleExists.id,
                                action: action
                              }
                            },
                            include: {
                              permission: true
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      })

      // Find the path that grants this permission
      for (const membership of userWithDetails.groupMemberships) {
        for (const groupRole of membership.group.roles) {
          if (groupRole.role.permissions.length > 0) {
            grantingPath = {
              group: {
                id: membership.group.id,
                name: membership.group.name
              },
              role: {
                id: groupRole.role.id,
                name: groupRole.role.name
              },
              permission: {
                id: relevantPermission.id,
                action: relevantPermission.action,
                module: relevantPermission.module
              }
            }
            break
          }
        }
        if (grantingPath) break
      }
    }

    logger.info(`Action simulation by ${req.user.email}: ${targetUser.email} -> ${module}:${action} = ${hasAccess}`)

    res.json({
      success: true,
      data: {
        simulation: {
          user: targetUser,
          module: module,
          action: action,
          hasAccess: hasAccess,
          timestamp: new Date().toISOString()
        },
        details: {
          permission: relevantPermission || null,
          grantingPath: grantingPath
        }
      }
    })
  } catch (error) {
    logger.error('Simulate action error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to simulate action'
    })
  }
})

/**
 * @route   GET /api/permissions/check
 * @desc    Check if current user has specific permission
 * @access  Private
 */
router.get('/permissions/check', async (req, res) => {
  try {
    const { module, action } = req.query

    if (!module || !action) {
      return res.status(400).json({
        success: false,
        error: 'Module and action parameters are required'
      })
    }

    const hasAccess = await hasPermission(req.user.id, module, action)

    res.json({
      success: true,
      data: {
        user: {
          id: req.user.id,
          email: req.user.email
        },
        module,
        action,
        hasAccess,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    logger.error('Permission check error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to check permission'
    })
  }
})

/**
 * @route   GET /api/modules/available
 * @desc    Get all available modules and their permissions
 * @access  Private
 */
router.get('/modules/available', async (req, res) => {
  try {
    const modules = await prisma.module.findMany({
      where: { isActive: true },
      include: {
        permissions: {
          where: { isActive: true },
          select: {
            id: true,
            action: true,
            description: true
          },
          orderBy: { action: 'asc' }
        }
      },
      orderBy: { name: 'asc' }
    })

    res.json({
      success: true,
      data: { modules }
    })
  } catch (error) {
    logger.error('Get available modules error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get available modules'
    })
  }
})

export default router
