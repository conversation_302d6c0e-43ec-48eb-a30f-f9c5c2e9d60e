import logger from '../utils/logger.js'

/**
 * Custom error classes
 */
class AppError extends Error {
  constructor(message, statusCode, isOperational = true) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.timestamp = new Date().toISOString()

    Error.captureStackTrace(this, this.constructor)
  }
}

class ValidationError extends AppError {
  constructor(message, details = []) {
    super(message, 400)
    this.details = details
    this.type = 'VALIDATION_ERROR'
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401)
    this.type = 'AUTHENTICATION_ERROR'
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403)
    this.type = 'AUTHORIZATION_ERROR'
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404)
    this.type = 'NOT_FOUND_ERROR'
  }
}

class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409)
    this.type = 'CONFLICT_ERROR'
  }
}

class RateLimitError extends AppError {
  constructor(message = 'Too many requests') {
    super(message, 429)
    this.type = 'RATE_LIMIT_ERROR'
  }
}

/**
 * Enhanced error handler with comprehensive error mapping
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err }
  error.message = err.message

  // Create error context for logging
  const errorContext = {
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name,
      code: err.code
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      body: req.method !== 'GET' ? req.body : undefined,
      query: req.query,
      params: req.params
    },
    timestamp: new Date().toISOString()
  }

  // Log error with context
  if (err.isOperational) {
    logger.warn('Operational error:', errorContext)
  } else {
    logger.error('System error:', errorContext)
  }

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found'
    error = { message, statusCode: 404 }
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered'
    error = { message, statusCode: 400 }
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message)
    error = { message, statusCode: 400 }
  }

  // Prisma errors
  if (err.code === 'P2002') {
    const message = 'Duplicate field value entered'
    error = { message, statusCode: 400 }
  }

  if (err.code === 'P2025') {
    const message = 'Record not found'
    error = { message, statusCode: 404 }
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token'
    error = { message, statusCode: 401 }
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired'
    error = { message, statusCode: 401 }
  }

  // Prepare response
  const response = {
    success: false,
    error: error.message || 'Server Error',
    type: error.type || 'UNKNOWN_ERROR',
    timestamp: new Date().toISOString(),
    requestId: req.id || 'unknown'
  }

  // Add details for validation errors
  if (error.details) {
    response.details = error.details
  }

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    response.stack = err.stack
    response.originalError = err
  }

  // Send error response
  res.status(error.statusCode || 500).json(response)
}

/**
 * Handle unhandled promise rejections
 */
const handleUnhandledRejection = (reason, promise) => {
  logger.error('Unhandled Promise Rejection:', {
    reason: reason.message || reason,
    stack: reason.stack,
    promise
  })

  // Graceful shutdown
  process.exit(1)
}

/**
 * Handle uncaught exceptions
 */
const handleUncaughtException = error => {
  logger.error('Uncaught Exception:', {
    message: error.message,
    stack: error.stack
  })

  // Graceful shutdown
  process.exit(1)
}

export {
  errorHandler,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  handleUnhandledRejection,
  handleUncaughtException
}
